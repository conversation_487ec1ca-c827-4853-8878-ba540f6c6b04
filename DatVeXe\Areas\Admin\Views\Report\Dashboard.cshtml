@{
    ViewData["Title"] = "B<PERSON>o cáo thống kê";
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
}

<style>
    .stats-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-card h3, .stats-card p {
        color: white !important;
    }

    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        color: white !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .chart-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 1rem;
    }
</style>

<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="text-dark mb-1">
            <i class="fas fa-chart-line text-primary me-2"></i>
            Báo cáo thống kê
        </h2>
        <p class="text-muted mb-0">Tổng quan về hoạt động kinh doanh và doanh thu</p>
    </div>
    <div class="d-flex gap-2">
        <a asp-action="Revenue" class="btn btn-outline-primary">
            <i class="fas fa-money-bill-wave me-1"></i>
            Báo cáo doanh thu
        </a>
        <a asp-action="Customer" class="btn btn-outline-info">
            <i class="fas fa-users me-1"></i>
            Báo cáo khách hàng
        </a>
        <a asp-action="Route" class="btn btn-outline-success">
            <i class="fas fa-route me-1"></i>
            Báo cáo tuyến đường
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongDoanhThu.ToString("N0")</h3>
                        <p class="mb-0 small">Tổng doanh thu (VNĐ)</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-money-bill-wave stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-success text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.DoanhThuThangNay.ToString("N0")</h3>
                        <p class="mb-0 small">Doanh thu tháng này (VNĐ)</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-calendar-alt stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongVeBan</h3>
                        <p class="mb-0 small">Tổng vé đã bán</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-ticket-alt stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-info text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TyLeLapDay.ToString("F1")%</h3>
                        <p class="mb-0 small">Tỷ lệ lấp đầy</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-percentage stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-line me-2"></i>
                Doanh thu theo tháng (12 tháng gần nhất)
            </h5>
            <canvas id="revenueChart" height="100"></canvas>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-pie me-2"></i>
                Top 5 tuyến đường
            </h5>
            <canvas id="topRoutesChart" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row">
    <div class="col-lg-6">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-bar me-2"></i>
                Số vé bán theo tháng
            </h5>
            <canvas id="ticketSalesChart" height="150"></canvas>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-users me-2"></i>
                Khách hàng mới theo tháng
            </h5>
            <canvas id="newCustomersChart" height="150"></canvas>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Doanh thu theo tháng
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueData = @Html.Raw(Json.Serialize(ViewBag.DoanhThuTheoThang));
        
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: revenueData.map(d => d.Thang),
                datasets: [{
                    label: 'Doanh thu (VNĐ)',
                    data: revenueData.map(d => d.DoanhThu),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' VNĐ';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Top tuyến đường
        const topRoutesCtx = document.getElementById('topRoutesChart').getContext('2d');
        const topRoutesData = @Html.Raw(Json.Serialize(ViewBag.TopTuyenDuong));
        
        new Chart(topRoutesCtx, {
            type: 'doughnut',
            data: {
                labels: topRoutesData.map(d => d.TuyenDuong),
                datasets: [{
                    data: topRoutesData.map(d => d.DoanhThu),
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Số vé bán theo tháng
        const ticketSalesCtx = document.getElementById('ticketSalesChart').getContext('2d');
        const ticketSalesData = @Html.Raw(Json.Serialize(ViewBag.VeBanTheoThang));
        
        new Chart(ticketSalesCtx, {
            type: 'bar',
            data: {
                labels: ticketSalesData.map(d => d.Thang),
                datasets: [{
                    label: 'Số vé',
                    data: ticketSalesData.map(d => d.SoVe),
                    backgroundColor: '#28a745'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Khách hàng mới theo tháng
        const newCustomersCtx = document.getElementById('newCustomersChart').getContext('2d');
        const newCustomersData = @Html.Raw(Json.Serialize(ViewBag.KhachHangMoiTheoThang));
        
        new Chart(newCustomersCtx, {
            type: 'bar',
            data: {
                labels: newCustomersData.map(d => d.Thang),
                datasets: [{
                    label: 'Khách hàng mới',
                    data: newCustomersData.map(d => d.SoKhachHang),
                    backgroundColor: '#17a2b8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
}
