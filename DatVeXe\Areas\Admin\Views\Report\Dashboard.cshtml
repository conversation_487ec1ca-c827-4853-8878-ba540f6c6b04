@{
    ViewData["Title"] = "B<PERSON>o cáo thống kê";
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
}

<style>
    .stats-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-card h3, .stats-card p {
        color: white !important;
    }

    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        color: white !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    }

    .bg-gradient-dark {
        background: linear-gradient(135deg, #343a40 0%, #212529 100%) !important;
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .chart-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .chart-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 1rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
    }

    .stats-card .opacity-75 {
        opacity: 0.85 !important;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 8px;
    }

    .route-indicator {
        width: 4px;
        height: 20px;
        border-radius: 2px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .btn-outline-primary:hover,
    .btn-outline-info:hover,
    .btn-outline-success:hover,
    .btn-outline-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn {
        transition: all 0.3s ease;
        border: 2px solid;
        text-decoration: none;
    }

    .quick-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
    }

    .table td {
        vertical-align: middle;
        border-color: #e9ecef;
    }
</style>

<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="text-dark mb-1">
            <i class="fas fa-chart-line text-primary me-2"></i>
            Báo cáo thống kê
        </h2>
        <p class="text-muted mb-0">Tổng quan về hoạt động kinh doanh và doanh thu</p>
    </div>
    <div class="d-flex gap-2">
        <a asp-action="Revenue" class="btn btn-outline-primary">
            <i class="fas fa-money-bill-wave me-1"></i>
            Báo cáo doanh thu
        </a>
        <a asp-action="Customer" class="btn btn-outline-info">
            <i class="fas fa-users me-1"></i>
            Báo cáo khách hàng
        </a>
        <a asp-action="Route" class="btn btn-outline-success">
            <i class="fas fa-route me-1"></i>
            Báo cáo tuyến đường
        </a>
    </div>
</div>

<!-- Statistics Cards Row 1 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongDoanhThu.ToString("N0")</h3>
                        <p class="mb-0 small">Tổng doanh thu (VNĐ)</p>
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            Tháng này: @ViewBag.DoanhThuThangNay.ToString("N0")
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-money-bill-wave stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-success text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongVeBan.ToString("N0")</h3>
                        <p class="mb-0 small">Tổng vé đã bán</p>
                        <small class="opacity-75">
                            <i class="fas fa-calendar me-1"></i>
                            Tháng này: @ViewBag.VeBanThangNay
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-ticket-alt stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongKhachHang.ToString("N0")</h3>
                        <p class="mb-0 small">Tổng khách hàng</p>
                        <small class="opacity-75">
                            <i class="fas fa-user-plus me-1"></i>
                            Mới tháng này: @ViewBag.KhachHangMoiThangNay
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-users stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-info text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TyLeLapDay.ToString("F1")%</h3>
                        <p class="mb-0 small">Tỷ lệ lấp đầy</p>
                        <small class="opacity-75">
                            <i class="fas fa-chart-line me-1"></i>
                            Hiệu suất cao
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-percentage stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards Row 2 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-secondary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.TongChuyenXe.ToString("N0")</h3>
                        <p class="mb-0 small">Tổng chuyến xe</p>
                        <small class="opacity-75">
                            <i class="fas fa-calendar-day me-1"></i>
                            Hôm nay: @ViewBag.ChuyenXeHomNay
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-bus stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card bg-gradient-dark text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1 fw-bold">@ViewBag.DoanhThuNamNay.ToString("N0")</h3>
                        <p class="mb-0 small">Doanh thu năm nay (VNĐ)</p>
                        <small class="opacity-75">
                            <i class="fas fa-chart-bar me-1"></i>
                            Tăng trưởng tốt
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-chart-area stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card stats-card bg-light border-0">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h5 class="mb-2 text-dark fw-bold">
                            <i class="fas fa-clock text-primary me-2"></i>
                            Cập nhật lần cuối
                        </h5>
                        <p class="mb-1 text-muted">@DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</p>
                        <small class="text-success">
                            <i class="fas fa-sync-alt me-1"></i>
                            Dữ liệu realtime
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-refresh me-1"></i>
                            Làm mới
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-line me-2"></i>
                Doanh thu theo tháng (12 tháng gần nhất)
            </h5>
            <canvas id="revenueChart" height="100"></canvas>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-pie me-2"></i>
                Top 5 tuyến đường
            </h5>
            <canvas id="topRoutesChart" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row">
    <div class="col-lg-6">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-chart-bar me-2"></i>
                Số vé bán theo tháng
            </h5>
            <canvas id="ticketSalesChart" height="150"></canvas>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-users me-2"></i>
                Khách hàng mới theo tháng
            </h5>
            <canvas id="newCustomersChart" height="150"></canvas>
        </div>
    </div>
</div>

<!-- Top Routes Table -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="chart-title mb-0">
                    <i class="fas fa-route me-2"></i>
                    Top 10 tuyến đường theo doanh thu
                </h5>
                <div class="btn-group">
                    <a href="@Url.Action("ExportCsv", new { reportType = "overview" })" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-csv me-1"></i>
                        Xuất CSV
                    </a>
                    <a href="@Url.Action("Route")" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        Xem chi tiết
                    </a>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="fw-bold">#</th>
                            <th class="fw-bold">Tuyến đường</th>
                            <th class="fw-bold text-end">Doanh thu (VNĐ)</th>
                            <th class="fw-bold text-end">Số vé</th>
                            <th class="fw-bold text-center">Hiệu suất</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            var topRoutes = ViewBag.TopTuyenDuong as IEnumerable<dynamic>;
                            int index = 1;
                        }
                        @if (topRoutes != null)
                        {
                            @foreach (var route in topRoutes.Take(10))
                            {
                                <tr>
                                    <td class="fw-bold text-primary">@index</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="route-indicator bg-primary me-2"></div>
                                            <span class="fw-medium">@route.TuyenDuong</span>
                                        </div>
                                    </td>
                                    <td class="text-end fw-bold text-success">@route.DoanhThu.ToString("N0")</td>
                                    <td class="text-end">
                                        <span class="badge bg-info">@route.SoVe vé</span>
                                    </td>
                                    <td class="text-center">
                                        @{
                                            var performance = route.DoanhThu > 10000000 ? "Xuất sắc" :
                                                            route.DoanhThu > 5000000 ? "Tốt" :
                                                            route.DoanhThu > 1000000 ? "Trung bình" : "Thấp";
                                            var badgeClass = route.DoanhThu > 10000000 ? "bg-success" :
                                                           route.DoanhThu > 5000000 ? "bg-primary" :
                                                           route.DoanhThu > 1000000 ? "bg-warning" : "bg-secondary";
                                        }
                                        <span class="badge @badgeClass">@performance</span>
                                    </td>
                                </tr>
                                index++;
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu tuyến đường
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <h5 class="chart-title">
                <i class="fas fa-bolt me-2"></i>
                Thao tác nhanh
            </h5>
            <div class="row g-3">
                <div class="col-lg-3 col-md-6">
                    <a href="@Url.Action("Revenue")" class="btn btn-outline-primary w-100 py-3">
                        <i class="fas fa-chart-line fa-2x mb-2 d-block"></i>
                        <strong>Báo cáo doanh thu</strong>
                        <small class="d-block text-muted">Chi tiết theo thời gian</small>
                    </a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <a href="@Url.Action("Customer")" class="btn btn-outline-info w-100 py-3">
                        <i class="fas fa-users fa-2x mb-2 d-block"></i>
                        <strong>Báo cáo khách hàng</strong>
                        <small class="d-block text-muted">Phân tích khách hàng</small>
                    </a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <a href="@Url.Action("Route")" class="btn btn-outline-success w-100 py-3">
                        <i class="fas fa-route fa-2x mb-2 d-block"></i>
                        <strong>Báo cáo tuyến đường</strong>
                        <small class="d-block text-muted">Hiệu suất tuyến</small>
                    </a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <a href="@Url.Action("ExportCsv", new { reportType = "overview" })" class="btn btn-outline-warning w-100 py-3">
                        <i class="fas fa-download fa-2x mb-2 d-block"></i>
                        <strong>Xuất báo cáo</strong>
                        <small class="d-block text-muted">Tải về CSV/Excel</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
    <script>
        // Doanh thu theo tháng
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueData = @Html.Raw(Json.Serialize(ViewBag.DoanhThuTheoThang));
        
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: revenueData.map(d => d.Thang),
                datasets: [{
                    label: 'Doanh thu (VNĐ)',
                    data: revenueData.map(d => d.DoanhThu),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 3,
                    pointBackgroundColor: '#007bff',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 1000000).toFixed(1) + 'M VNĐ';
                            },
                            font: {
                                weight: 'bold'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#007bff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return 'Doanh thu: ' + context.parsed.y.toLocaleString() + ' VNĐ';
                            }
                        }
                    }
                }
            }
        });

        // Top tuyến đường
        const topRoutesCtx = document.getElementById('topRoutesChart').getContext('2d');
        const topRoutesData = @Html.Raw(Json.Serialize(ViewBag.TopTuyenDuong));

        new Chart(topRoutesCtx, {
            type: 'doughnut',
            data: {
                labels: topRoutesData.slice(0, 5).map(d => d.TuyenDuong),
                datasets: [{
                    data: topRoutesData.slice(0, 5).map(d => d.DoanhThu),
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#6c757d'
                    ],
                    borderWidth: 3,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 5,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#007bff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed.toLocaleString() + ' VNĐ (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Số vé bán theo tháng
        const ticketSalesCtx = document.getElementById('ticketSalesChart').getContext('2d');
        const ticketSalesData = @Html.Raw(Json.Serialize(ViewBag.VeBanTheoThang));
        
        new Chart(ticketSalesCtx, {
            type: 'bar',
            data: {
                labels: ticketSalesData.map(d => d.Thang),
                datasets: [{
                    label: 'Số vé',
                    data: ticketSalesData.map(d => d.SoVe),
                    backgroundColor: '#28a745'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Khách hàng mới theo tháng
        const newCustomersCtx = document.getElementById('newCustomersChart').getContext('2d');
        const newCustomersData = @Html.Raw(Json.Serialize(ViewBag.KhachHangMoiTheoThang));
        
        new Chart(newCustomersCtx, {
            type: 'bar',
            data: {
                labels: newCustomersData.map(d => d.Thang),
                datasets: [{
                    label: 'Khách hàng mới',
                    data: newCustomersData.map(d => d.SoKhachHang),
                    backgroundColor: '#17a2b8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Animate stats cards on page load
        document.addEventListener('DOMContentLoaded', function() {
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Animate chart containers
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach((container, index) => {
                container.style.opacity = '0';
                container.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    container.style.transition = 'all 0.8s ease';
                    container.style.opacity = '1';
                    container.style.transform = 'translateY(0)';
                }, 500 + (index * 200));
            });

            // Add click animation to quick action buttons
            const quickActionBtns = document.querySelectorAll('.btn-outline-primary, .btn-outline-info, .btn-outline-success, .btn-outline-warning');
            quickActionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // Auto refresh data every 5 minutes
        setInterval(function() {
            const lastUpdate = document.querySelector('.text-success small');
            if (lastUpdate) {
                const now = new Date();
                lastUpdate.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Cập nhật: ' +
                    now.toLocaleTimeString('vi-VN');
            }
        }, 300000); // 5 minutes
    </script>

    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @@keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
}
